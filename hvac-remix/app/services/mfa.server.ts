/**
 * Multi-Factor Authentication Service - Divine Quality Estate Security
 * Provides comprehensive MFA functionality for enhanced security
 */

import { authenticator } from 'otplib';
import QRCode from 'qrcode';
import crypto from 'crypto';
import { prisma } from '~/db.server';

export interface MFASetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export interface MFAVerificationResult {
  success: boolean;
  error?: string;
}

/**
 * Generate MFA secret and QR code for user setup
 */
export async function setupMFA(userId: string, userEmail: string): Promise<MFASetupResult> {
  try {
    // Generate secret
    const secret = authenticator.generateSecret();
    
    // Generate service name for QR code
    const serviceName = process.env.APP_NAME || 'HVAC CRM';
    const otpAuthUrl = authenticator.keyuri(userEmail, serviceName, secret);
    
    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(otpAuthUrl);
    
    // Generate backup codes
    const backupCodes = generateBackupCodes();
    
    // Store MFA configuration (but don't enable until verified)
    await prisma.userMFA.upsert({
      where: { userId },
      update: {
        secret,
        backupCodes: backupCodes.map(code => hashBackupCode(code)),
        isEnabled: false,
        createdAt: new Date()
      },
      create: {
        userId,
        secret,
        backupCodes: backupCodes.map(code => hashBackupCode(code)),
        isEnabled: false
      }
    });
    
    return {
      secret,
      qrCodeUrl,
      backupCodes
    };
  } catch (error) {
    console.error('MFA setup failed:', error);
    throw new Error('Failed to setup MFA');
  }
}

/**
 * Verify MFA token and enable MFA for user
 */
export async function verifyAndEnableMFA(
  userId: string, 
  token: string
): Promise<MFAVerificationResult> {
  try {
    const mfaConfig = await prisma.userMFA.findUnique({
      where: { userId }
    });
    
    if (!mfaConfig) {
      return { success: false, error: 'MFA not configured' };
    }
    
    const isValid = authenticator.verify({
      token,
      secret: mfaConfig.secret
    });
    
    if (!isValid) {
      return { success: false, error: 'Invalid token' };
    }
    
    // Enable MFA
    await prisma.userMFA.update({
      where: { userId },
      data: { isEnabled: true }
    });
    
    return { success: true };
  } catch (error) {
    console.error('MFA verification failed:', error);
    return { success: false, error: 'Verification failed' };
  }
}

/**
 * Verify MFA token for login
 */
export async function verifyMFAToken(
  userId: string, 
  token: string
): Promise<MFAVerificationResult> {
  try {
    const mfaConfig = await prisma.userMFA.findUnique({
      where: { userId }
    });
    
    if (!mfaConfig || !mfaConfig.isEnabled) {
      return { success: false, error: 'MFA not enabled' };
    }
    
    // Check if it's a backup code
    if (token.length === 8) {
      return verifyBackupCode(userId, token);
    }
    
    // Verify TOTP token
    const isValid = authenticator.verify({
      token,
      secret: mfaConfig.secret,
      window: 1 // Allow 1 step tolerance
    });
    
    if (!isValid) {
      return { success: false, error: 'Invalid token' };
    }
    
    return { success: true };
  } catch (error) {
    console.error('MFA token verification failed:', error);
    return { success: false, error: 'Verification failed' };
  }
}

/**
 * Verify backup code
 */
async function verifyBackupCode(
  userId: string, 
  code: string
): Promise<MFAVerificationResult> {
  try {
    const mfaConfig = await prisma.userMFA.findUnique({
      where: { userId }
    });
    
    if (!mfaConfig) {
      return { success: false, error: 'MFA not configured' };
    }
    
    const hashedCode = hashBackupCode(code);
    const codeIndex = mfaConfig.backupCodes.indexOf(hashedCode);
    
    if (codeIndex === -1) {
      return { success: false, error: 'Invalid backup code' };
    }
    
    // Remove used backup code
    const updatedCodes = [...mfaConfig.backupCodes];
    updatedCodes.splice(codeIndex, 1);
    
    await prisma.userMFA.update({
      where: { userId },
      data: { backupCodes: updatedCodes }
    });
    
    return { success: true };
  } catch (error) {
    console.error('Backup code verification failed:', error);
    return { success: false, error: 'Verification failed' };
  }
}

/**
 * Check if user has MFA enabled
 */
export async function isMfaEnabled(userId: string): Promise<boolean> {
  try {
    const mfaConfig = await prisma.userMFA.findUnique({
      where: { userId }
    });
    
    return mfaConfig?.isEnabled || false;
  } catch (error) {
    console.error('MFA status check failed:', error);
    return false;
  }
}

/**
 * Disable MFA for user
 */
export async function disableMFA(userId: string): Promise<boolean> {
  try {
    await prisma.userMFA.delete({
      where: { userId }
    });
    
    return true;
  } catch (error) {
    console.error('MFA disable failed:', error);
    return false;
  }
}

/**
 * Generate new backup codes
 */
export async function generateNewBackupCodes(userId: string): Promise<string[]> {
  try {
    const backupCodes = generateBackupCodes();
    
    await prisma.userMFA.update({
      where: { userId },
      data: {
        backupCodes: backupCodes.map(code => hashBackupCode(code))
      }
    });
    
    return backupCodes;
  } catch (error) {
    console.error('Backup code generation failed:', error);
    throw new Error('Failed to generate backup codes');
  }
}

/**
 * Generate backup codes
 */
function generateBackupCodes(): string[] {
  const codes: string[] = [];
  for (let i = 0; i < 10; i++) {
    codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
  }
  return codes;
}

/**
 * Hash backup code for secure storage
 */
function hashBackupCode(code: string): string {
  return crypto.createHash('sha256').update(code).digest('hex');
}

/**
 * Verify if MFA is required for user
 */
export async function isMfaVerified(userId: string, sessionId: string): Promise<boolean> {
  try {
    const session = await prisma.session.findUnique({
      where: { id: sessionId }
    });
    
    if (!session || session.userId !== userId) {
      return false;
    }
    
    // Check if MFA is enabled for user
    const mfaEnabled = await isMfaEnabled(userId);
    
    if (!mfaEnabled) {
      return true; // MFA not required
    }
    
    // Check if session has MFA verification
    return session.mfaVerified || false;
  } catch (error) {
    console.error('MFA verification check failed:', error);
    return false;
  }
}
