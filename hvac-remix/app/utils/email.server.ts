/**
 * Email Service - Divine Quality Estate Communications
 * Comprehensive email functionality with templates and tracking
 */

import nodemailer from 'nodemailer';
import { prisma } from '~/db.server';
import { recordMetric, log } from '~/utils/monitoring.server';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export interface EmailTemplate {
  name: string;
  subject: string;
  html: string;
  text?: string;
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Email service class
 */
class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private templates: Map<string, EmailTemplate> = new Map();

  constructor() {
    this.initializeTransporter();
    this.loadTemplates();
  }

  /**
   * Initialize email transporter
   */
  private initializeTransporter() {
    try {
      const emailConfig = {
        host: process.env.SMTP_HOST || 'localhost',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      };

      if (!process.env.SMTP_HOST) {
        console.warn('SMTP not configured - emails will be logged only');
        return;
      }

      this.transporter = nodemailer.createTransporter(emailConfig);
      
      // Verify connection
      this.transporter.verify((error) => {
        if (error) {
          console.error('Email transporter verification failed:', error);
          this.transporter = null;
        } else {
          console.log('Email transporter ready');
        }
      });
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
    }
  }

  /**
   * Load email templates
   */
  private loadTemplates() {
    // Welcome email template
    this.templates.set('welcome', {
      name: 'welcome',
      subject: 'Welcome to HVAC CRM',
      html: `
        <h1>Welcome to HVAC CRM!</h1>
        <p>Hello {{firstName}},</p>
        <p>Welcome to our HVAC CRM system. Your account has been created successfully.</p>
        <p>You can now log in and start managing your HVAC services.</p>
        <p>Best regards,<br>The HVAC CRM Team</p>
      `,
      text: 'Welcome to HVAC CRM! Your account has been created successfully.',
    });

    // Service reminder template
    this.templates.set('service-reminder', {
      name: 'service-reminder',
      subject: 'Service Reminder - {{equipmentType}}',
      html: `
        <h1>Service Reminder</h1>
        <p>Hello {{customerName}},</p>
        <p>This is a reminder that your {{equipmentType}} is due for maintenance.</p>
        <p><strong>Equipment:</strong> {{equipmentModel}}</p>
        <p><strong>Last Service:</strong> {{lastServiceDate}}</p>
        <p><strong>Recommended Service Date:</strong> {{recommendedDate}}</p>
        <p>Please contact us to schedule your service appointment.</p>
        <p>Best regards,<br>{{companyName}}</p>
      `,
      text: 'Service reminder for your {{equipmentType}}. Please contact us to schedule maintenance.',
    });

    // Job completion template
    this.templates.set('job-completed', {
      name: 'job-completed',
      subject: 'Service Completed - {{jobTitle}}',
      html: `
        <h1>Service Completed</h1>
        <p>Hello {{customerName}},</p>
        <p>We have successfully completed the service work for:</p>
        <p><strong>Job:</strong> {{jobTitle}}</p>
        <p><strong>Technician:</strong> {{technicianName}}</p>
        <p><strong>Completion Date:</strong> {{completionDate}}</p>
        <p><strong>Summary:</strong> {{workSummary}}</p>
        <p>Thank you for choosing our services!</p>
        <p>Best regards,<br>{{companyName}}</p>
      `,
      text: 'Service completed for {{jobTitle}}. Thank you for choosing our services!',
    });
  }

  /**
   * Send email
   */
  async sendEmail(options: EmailOptions): Promise<EmailResult> {
    try {
      if (!this.transporter) {
        // Log email for development
        log({
          level: 'info',
          message: 'Email would be sent',
          metadata: {
            to: options.to,
            subject: options.subject,
            html: options.html?.substring(0, 100) + '...',
          },
        });
        
        return {
          success: true,
          messageId: 'dev-' + Date.now(),
        };
      }

      const mailOptions = {
        from: options.from || process.env.SMTP_FROM || '<EMAIL>',
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
        replyTo: options.replyTo,
        attachments: options.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);

      // Record success metric
      recordMetric('email_sent', 1, 'counter', { status: 'success' });

      // Log email sent
      await this.logEmailSent(options, result.messageId);

      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      console.error('Failed to send email:', error);

      // Record failure metric
      recordMetric('email_sent', 1, 'counter', { status: 'failure' });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send email using template
   */
  async sendTemplateEmail(
    templateName: string,
    to: string | string[],
    variables: Record<string, string>,
    options?: Partial<EmailOptions>
  ): Promise<EmailResult> {
    try {
      const template = this.templates.get(templateName);
      
      if (!template) {
        throw new Error(`Template '${templateName}' not found`);
      }

      // Replace variables in template
      const subject = this.replaceVariables(template.subject, variables);
      const html = this.replaceVariables(template.html, variables);
      const text = template.text ? this.replaceVariables(template.text, variables) : undefined;

      return await this.sendEmail({
        to,
        subject,
        html,
        text,
        ...options,
      });
    } catch (error) {
      console.error('Failed to send template email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Replace variables in template
   */
  private replaceVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, value);
    }
    
    return result;
  }

  /**
   * Log email sent to database
   */
  private async logEmailSent(options: EmailOptions, messageId: string) {
    try {
      await prisma.emailLog.create({
        data: {
          to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
          subject: options.subject,
          messageId,
          status: 'sent',
          sentAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Failed to log email:', error);
    }
  }

  /**
   * Get email statistics
   */
  async getEmailStats(days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const [totalSent, totalFailed] = await Promise.all([
        prisma.emailLog.count({
          where: {
            status: 'sent',
            sentAt: { gte: startDate },
          },
        }),
        prisma.emailLog.count({
          where: {
            status: 'failed',
            sentAt: { gte: startDate },
          },
        }),
      ]);

      return {
        totalSent,
        totalFailed,
        successRate: totalSent + totalFailed > 0 ? (totalSent / (totalSent + totalFailed)) * 100 : 0,
        period: `${days} days`,
      };
    } catch (error) {
      console.error('Failed to get email stats:', error);
      throw new Error('Failed to get email statistics');
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();

// Export convenience functions
export const sendEmail = (options: EmailOptions) => emailService.sendEmail(options);
export const sendTemplateEmail = (
  templateName: string,
  to: string | string[],
  variables: Record<string, string>,
  options?: Partial<EmailOptions>
) => emailService.sendTemplateEmail(templateName, to, variables, options);

/**
 * Send welcome email to new user
 */
export async function sendWelcomeEmail(userEmail: string, firstName: string): Promise<EmailResult> {
  return sendTemplateEmail('welcome', userEmail, { firstName });
}

/**
 * Send service reminder email
 */
export async function sendServiceReminderEmail(
  customerEmail: string,
  customerName: string,
  equipmentType: string,
  equipmentModel: string,
  lastServiceDate: string,
  recommendedDate: string,
  companyName: string
): Promise<EmailResult> {
  return sendTemplateEmail('service-reminder', customerEmail, {
    customerName,
    equipmentType,
    equipmentModel,
    lastServiceDate,
    recommendedDate,
    companyName,
  });
}

/**
 * Send job completion email
 */
export async function sendJobCompletionEmail(
  customerEmail: string,
  customerName: string,
  jobTitle: string,
  technicianName: string,
  completionDate: string,
  workSummary: string,
  companyName: string
): Promise<EmailResult> {
  return sendTemplateEmail('job-completed', customerEmail, {
    customerName,
    jobTitle,
    technicianName,
    completionDate,
    workSummary,
    companyName,
  });
}
