/**
 * 👤 COMPREHENSIVE CUSTOMER PROFILE DASHBOARD
 *
 * Advanced customer profile management with communication hub,
 * document management, and relationship mapping.
 *
 * Philosophy: "Every customer is a universe of possibilities"
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import {
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  MessageSquare,
  FileText,
  Camera,
  Settings,
  Heart,
  TrendingUp,
  AlertTriangle,
  Clock,
  DollarSign,
  Users,
  Building,
  Star,
  Shield
} from 'lucide-react';
import {
  EnhancedCustomerProfile,
  CommunicationChannelType,
  CommunicationSentiment
} from '~/models/customer-profile-management.server';
import { CommunicationAnalytics } from '~/models/communication-hub.server';
import { DocumentAnalytics } from '~/models/document-management.server';

interface CustomerProfileDashboardProps {
  customerId: string;
  onActionClick?: (action: string, data?: any) => void;
}

export function CustomerProfileDashboard({
  customerId,
  onActionClick
}: CustomerProfileDashboardProps) {
  const [customerProfile, setCustomerProfile] = useState<EnhancedCustomerProfile | null>(null);
  const [communicationAnalytics, setCommunicationAnalytics] = useState<CommunicationAnalytics | null>(null);
  const [documentAnalytics, setDocumentAnalytics] = useState<DocumentAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadCustomerData();
  }, [customerId]);

  const loadCustomerData = async () => {
    try {
      setLoading(true);

      // Load customer profile
      const profileResponse = await fetch(`/api/customer-profile/${customerId}`);
      const profile = await profileResponse.json();
      setCustomerProfile(profile);

      // Load communication analytics
      const commResponse = await fetch(`/api/communication-hub/analytics/${customerId}`);
      const commAnalytics = await commResponse.json();
      setCommunicationAnalytics(commAnalytics);

      // Load document analytics
      const docResponse = await fetch(`/api/document-management/analytics?customerId=${customerId}`);
      const docAnalytics = await docResponse.json();
      setDocumentAnalytics(docAnalytics);

    } catch (error) {
      console.error('Failed to load customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'COMMERCIAL': return <Building className="h-5 w-5" />;
      case 'INDUSTRIAL': return <Settings className="h-5 w-5" />;
      default: return <User className="h-5 w-5" />;
    }
  };

  const getCustomerStatusColor = (status: string): string => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800';
      case 'SUSPENDED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSentimentColor = (sentiment: CommunicationSentiment): string => {
    switch (sentiment) {
      case CommunicationSentiment.VERY_POSITIVE: return 'text-green-600';
      case CommunicationSentiment.POSITIVE: return 'text-green-500';
      case CommunicationSentiment.NEUTRAL: return 'text-gray-500';
      case CommunicationSentiment.NEGATIVE: return 'text-red-500';
      case CommunicationSentiment.VERY_NEGATIVE: return 'text-red-600';
      default: return 'text-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!customerProfile) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-600">Customer profile not found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Customer Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {getCustomerTypeIcon(customerProfile.basicInfo.type)}
                <div>
                  <h1 className="text-2xl font-bold">{customerProfile.basicInfo.name}</h1>
                  <p className="text-gray-600">Account #{customerProfile.basicInfo.accountNumber}</p>
                </div>
              </div>
              <Badge className={getCustomerStatusColor(customerProfile.basicInfo.status)}>
                {customerProfile.basicInfo.status}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => onActionClick?.('edit_profile')}>
                <Settings className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
              <Button onClick={() => onActionClick?.('new_service_order')}>
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Service
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Contact Information */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-gray-700">Contact Information</h3>
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{customerProfile.contactInfo.primaryPhone}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{customerProfile.contactInfo.email}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span>
                    {customerProfile.contactInfo.primaryAddress.city}, {customerProfile.contactInfo.primaryAddress.state}
                  </span>
                </div>
              </div>
            </div>

            {/* Service Statistics */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-gray-700">Service Statistics</h3>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Total Orders:</span>
                  <span className="font-medium">{customerProfile.statistics.totalServiceOrders}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total Revenue:</span>
                  <span className="font-medium text-green-600">
                    ${customerProfile.statistics.totalRevenue.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Avg Job Value:</span>
                  <span className="font-medium">
                    ${customerProfile.statistics.averageJobValue.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Communication Summary */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-gray-700">Communication</h3>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Frequency:</span>
                  <span className="font-medium">{customerProfile.statistics.communicationFrequency}/month</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Response Rate:</span>
                  <span className="font-medium text-green-600">
                    {customerProfile.statistics.responseRate}%
                  </span>
                </div>
                {customerProfile.statistics.satisfactionScore && (
                  <div className="flex justify-between text-sm">
                    <span>Satisfaction:</span>
                    <span className="font-medium text-yellow-600">
                      {customerProfile.statistics.satisfactionScore}/5 ⭐
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-2">
              <h3 className="font-medium text-sm text-gray-700">Quick Actions</h3>
              <div className="space-y-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => onActionClick?.('send_message')}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Send Message
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => onActionClick?.('view_documents')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  View Documents
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => onActionClick?.('view_equipment')}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  View Equipment
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="communication">Communication</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="equipment">Equipment</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Customer Since</p>
                    <p className="text-2xl font-bold">
                      {new Date(customerProfile.createdAt).getFullYear()}
                    </p>
                    <p className="text-sm text-gray-600">
                      {Math.floor((Date.now() - customerProfile.createdAt.getTime()) / (365 * 24 * 60 * 60 * 1000))} years
                    </p>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Last Service</p>
                    <p className="text-2xl font-bold">
                      {customerProfile.statistics.lastServiceDate ?
                        Math.floor((Date.now() - customerProfile.statistics.lastServiceDate.getTime()) / (24 * 60 * 60 * 1000))
                        : 'N/A'
                      }
                    </p>
                    <p className="text-sm text-gray-600">days ago</p>
                  </div>
                  <Clock className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Next Service</p>
                    <p className="text-2xl font-bold">
                      {customerProfile.statistics.nextScheduledService ?
                        Math.floor((customerProfile.statistics.nextScheduledService.getTime() - Date.now()) / (24 * 60 * 60 * 1000))
                        : 'TBD'
                      }
                    </p>
                    <p className="text-sm text-gray-600">days</p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-blue-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Email received about maintenance scheduling</p>
                    <p className="text-xs text-gray-600">2 hours ago</p>
                  </div>
                  <Badge variant="outline">Email</Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <Settings className="h-5 w-5 text-green-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Service order completed - HVAC maintenance</p>
                    <p className="text-xs text-gray-600">3 days ago</p>
                  </div>
                  <Badge variant="outline">Service</Badge>
                </div>
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <FileText className="h-5 w-5 text-purple-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Service contract renewed for 2024</p>
                    <p className="text-xs text-gray-600">1 week ago</p>
                  </div>
                  <Badge variant="outline">Contract</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="communication" className="space-y-6">
          {communicationAnalytics && (
            <>
              {/* Communication Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Communications</p>
                        <p className="text-2xl font-bold">{communicationAnalytics.summary.totalCommunications}</p>
                      </div>
                      <MessageSquare className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Response Rate</p>
                        <p className="text-2xl font-bold text-green-600">
                          {communicationAnalytics.summary.responseRate}%
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                        <p className="text-2xl font-bold">
                          {communicationAnalytics.summary.averageResponseTime}h
                        </p>
                      </div>
                      <Clock className="h-8 w-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Sentiment Score</p>
                        <p className={`text-2xl font-bold ${
                          communicationAnalytics.sentimentAnalysis.averageScore >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {communicationAnalytics.sentimentAnalysis.averageScore > 0 ? '+' : ''}
                          {communicationAnalytics.sentimentAnalysis.averageScore}
                        </p>
                      </div>
                      <Heart className="h-8 w-8 text-red-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Channel Breakdown */}
              <Card>
                <CardHeader>
                  <CardTitle>Communication Channels</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(communicationAnalytics.channelBreakdown)
                      .filter(([_, data]) => data.count > 0)
                      .map(([channel, data]) => (
                        <div key={channel} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                            <span className="font-medium">{channel}</span>
                          </div>
                          <div className="flex items-center gap-4">
                            <span className="text-sm text-gray-600">{data.count} messages</span>
                            <span className="text-sm text-gray-600">{data.percentage}%</span>
                            <Progress value={data.percentage} className="w-20 h-2" />
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          {documentAnalytics && (
            <>
              {/* Document Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Documents</p>
                        <p className="text-2xl font-bold">{documentAnalytics.totalDocuments}</p>
                      </div>
                      <FileText className="h-8 w-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Storage Used</p>
                        <p className="text-2xl font-bold">{documentAnalytics.storageUsed} MB</p>
                      </div>
                      <Shield className="h-8 w-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
                        <p className="text-2xl font-bold text-orange-600">
                          {documentAnalytics.expiringDocuments.length}
                        </p>
                      </div>
                      <AlertTriangle className="h-8 w-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <Button onClick={() => onActionClick?.('upload_document')}>
                        <FileText className="h-4 w-4 mr-2" />
                        Upload Document
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="equipment" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Equipment Overview</CardTitle>
                <Button onClick={() => onActionClick?.('add_equipment')}>
                  <Settings className="h-4 w-4 mr-2" />
                  Add Equipment
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">Main HVAC Unit</h4>
                      <Badge className="bg-green-100 text-green-800">Operational</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">Carrier XYZ-3000 • 3 Ton</p>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Health Score:</span>
                        <span className="text-green-600 font-medium">85%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Last Service:</span>
                        <span>15 days ago</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Next Service:</span>
                        <span>45 days</span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => onActionClick?.('view_equipment_details', 'equip_1')}
                    >
                      View Details
                    </Button>
                  </CardContent>
                </Card>

                <Card className="border-l-4 border-l-yellow-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">Thermostat System</h4>
                      <Badge className="bg-yellow-100 text-yellow-800">Maintenance Due</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">Honeywell Smart • WiFi</p>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Health Score:</span>
                        <span className="text-yellow-600 font-medium">72%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Last Service:</span>
                        <span>90 days ago</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Next Service:</span>
                        <span className="text-red-600">Overdue</span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => onActionClick?.('schedule_maintenance', 'equip_2')}
                    >
                      Schedule Service
                    </Button>
                  </CardContent>
                </Card>

                <Card className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">Air Handler</h4>
                      <Badge className="bg-blue-100 text-blue-800">Under Warranty</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">Trane AHU-2000 • 2.5 Ton</p>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Health Score:</span>
                        <span className="text-green-600 font-medium">92%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Last Service:</span>
                        <span>30 days ago</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Warranty:</span>
                        <span className="text-blue-600">2 years left</span>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full mt-3"
                      onClick={() => onActionClick?.('view_equipment_details', 'equip_3')}
                    >
                      View Details
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Service History</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Complete service history will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
